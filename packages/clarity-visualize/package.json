{"name": "clarity-visualize", "version": "0.8.20", "description": "An analytics library that uses web page interactions to generate aggregated insights", "author": "Microsoft Corp.", "license": "MIT", "main": "build/clarity.visualize.js", "module": "build/clarity.visualize.module.js", "unpkg": "build/clarity.visualize.min.js", "types": "types/index.d.ts", "keywords": ["clarity", "Microsoft", "interactions", "cursor", "pointer", "instrumentation", "analytics", "visualization"], "repository": {"type": "git", "url": "https://github.com/microsoft/clarity.git", "directory": "packages/clarity-visualize"}, "bugs": {"url": "https://github.com/Microsoft/clarity/issues"}, "dependencies": {"clarity-decode": "^0.8.20"}, "devDependencies": {"@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-terser": "^0.4.0", "@rollup/plugin-typescript": "^11.0.0", "rollup-plugin-string-import": "^1.2.5", "del-cli": "^5.0.0", "husky": "^8.0.0", "lint-staged": "^13.1.0", "rollup": "^3.0.0", "ts-node": "^10.1.0", "tslint": "^6.1.3", "typescript": "^4.3.5"}, "scripts": {"build": "yarn build:clean && yarn build:main", "build:main": "rollup -c rollup.config.ts --configPlugin @rollup/plugin-typescript", "build:clean": "del-cli build/*", "tslint": "tslint --project ./", "tslint:fix": "tslint --fix --project ./ --force"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.ts": ["tslint --format codeFrame"]}}