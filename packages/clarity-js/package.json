{"name": "clarity-js", "version": "0.8.20", "description": "An analytics library that uses web page interactions to generate aggregated insights", "author": "Microsoft Corp.", "license": "MIT", "main": "build/clarity.js", "module": "build/clarity.module.js", "unpkg": "build/clarity.min.js", "extended": "build/clarity.extended.js", "insight": "build/clarity.insight.js", "performance": "build/clarity.performance.js", "types": "types/index.d.ts", "keywords": ["clarity", "Microsoft", "interactions", "cursor", "pointer", "instrumentation", "analytics"], "repository": {"type": "git", "url": "https://github.com/microsoft/clarity.git", "directory": "packages/clarity-js"}, "bugs": {"url": "https://github.com/microsoft/clarity/issues"}, "devDependencies": {"@rollup/plugin-alias": "^4.0.0", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-terser": "^0.4.0", "@rollup/plugin-typescript": "^11.0.0", "@types/chai": "^4.3.0", "@types/mocha": "^10.0.0", "@types/resize-observer-browser": "^0.1.6", "chai": "^4.3.0", "del-cli": "^5.0.0", "husky": "^8.0.0", "lint-staged": "^13.1.0", "mocha": "^10.2.0", "playwright": "^1.6.2", "rollup": "^3.0.0", "ts-mocha": "^10.0.0", "tslib": "^2.3.0", "tslint": "^6.1.3", "typescript": "^4.3.5"}, "scripts": {"build": "yarn build:clean && yarn build:main", "build:main": "rollup -c rollup.config.ts --configPlugin @rollup/plugin-typescript", "build:clean": "del-cli build/*", "test": "ts-mocha -p test/tsconfig.test.json test/stub.test.ts", "tslint": "tslint --project ./", "tslint:fix": "tslint --fix --project ./ --force"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.ts": ["tslint --format codeFrame"]}}